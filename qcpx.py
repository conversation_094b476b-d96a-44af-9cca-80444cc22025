import kai.tensorflow as kai
<PERSON>ODEL_TRANS_ORIGIN='python'
import tensorflow as tf
import math
import logging

LOG_FORMAT = "%(asctime)s - %(levelname)s [%(filename)s:%(lineno)s - %(funcName)s] - %(message)s"
logging.basicConfig(level=logging.INFO, format=LOG_FORMAT)
logger = logging.getLogger(__name__)
from kai.tensorflow.config.ad_config.ktrain.AUC import auc as auc_eval
from kai.tensorflow.config.ad_config.ktrain.utils import metric_merge
import numpy as np

ssl_net_sizes = [64, 32]
interpret_sizes_1 = [16*2, 2]

trans_map = [
    [1.0, 0.0, 0.0],
    [0.0, 1.0, 0.0],
    [0.0, 0.0, 1.0]
]

def get_lookup_tbl_with_neg(K, neg_weight, neg_labels=[1, 8, 9]):
    tbl = []
    for i in range(1 << (K)):
        items = []
        for j in range(K):
            if i & (1 << j) != 0:
                items.append(1.)
            else:
                items.append(0.)
        items.append(1.)  # as sample weight
        tbl.append(items)

    for neg in neg_labels:
        tbl[neg][-1] = neg_weight  # for neg sample
    # tbl[neg_labels][-1] = neg_weight
    return tbl

class UserModel(object):

    def __init__(self):
        self.user_coupon_fea_slots = [i for i in range(475, 498)]

    def GetMetric(self):
        return self.eval_targets

    def GetPlaceHolder(self):
        return {"is_train": self.is_train_pl}

    def GetAUC(self):
        return {"auc": self.auc}
    
    def cast_greater(self,x,y):
        res = x - y
        return tf.ceil( tf.nn.tanh( res/(tf.abs(res)+1e-15) ) )

    def fc(self, _dnn_net_size, input, input_size, layer_size, i, is_train, use_bn, use_ln):
        weight_name = 'w'
        w = tf.get_variable(weight_name, [input_size, layer_size],
                            initializer=tf.random_normal_initializer(
                                stddev=1.0 / math.sqrt(float(input_size))),
                            trainable=True)
        bias_name = 'b'
        b = tf.get_variable(bias_name, [layer_size],
                            initializer=tf.zeros_initializer,
                            trainable=True)
        print("%s length=%d * %d" % (w.name, input_size, layer_size))
        print("%s length=%d" % (b.name, layer_size))

        o1 = tf.add(tf.matmul(input, w), b)
        if i != len(_dnn_net_size) - 1:
            if use_bn:
                print('bn in the {} layer'.format('share_bottom_layer_' + str(i)))
                o1 = kai.batch_norm(o1, 'bn')
            if use_ln:
                print('ln in the {} layer'.format('share_bottom_layer_' + str(i)))
                o1 = tf.contrib.layers.layer_norm(o1)
            o = tf.nn.relu(o1)
        else:
            o = o1

        return o

    def fc_dropout(self, _dnn_net_size, input, input_size, layer_size, i, use_bn, use_ln, dropout_rate, gussian_emb):
        weight_name = 'w'
        w = tf.get_variable(weight_name, [input_size, layer_size],
                            initializer=tf.random_normal_initializer(
                                stddev=1.0 / math.sqrt(float(input_size))),
                            trainable=True)
        bias_name = 'b'
        b = tf.get_variable(bias_name, [layer_size],
                            initializer=tf.zeros_initializer,
                            trainable=True)
        print("%s length=%d * %d" % (w.name, input_size, layer_size))
        print("%s length=%d" % (b.name, layer_size))

        o1 = tf.add(tf.matmul(input, w), b)
        if i != len(_dnn_net_size) - 1:
            if use_bn:
                print('bn in the {} layer'.format('share_bottom_layer_' + str(i)))
                o1 = BatchNorm(o1, is_train, 'bn')
            if use_ln:
                print('ln in the {} layer'.format('share_bottom_layer_' + str(i)))
                o1, normalized, gamma, std = self.layer_norm(o1)
            if i==2:
                gussian_emb = tf.slice(gussian_emb, [0, 0], [-1, 128])
            o1 = o1 * gussian_emb
            o = tf.nn.relu(o1)
        else:
            o = o1

        return o

    def fc_256(self, _dnn_net_size, input, input_size, layer_size, i, is_train, use_bn, use_ln):
        weight_name = 'w'
        w = tf.get_variable(weight_name, [input_size, layer_size],
                            initializer=tf.random_normal_initializer(
                                stddev=1.0 / math.sqrt(float(input_size))),
                            trainable=True)
        bias_name = 'b'
        b = tf.get_variable(bias_name, [layer_size],
                            initializer=tf.zeros_initializer,
                            trainable=True)
        print("%s length=%d * %d" % (w.name, input_size, layer_size))
        print("%s length=%d" % (b.name, layer_size))

        o1 = tf.add(tf.matmul(input, w), b)
        if i != len(_dnn_net_size) - 1:
            if use_bn:
                print('bn in the {} layer'.format('share_bottom_layer_' + str(i)))
                o1 = BatchNorm(o1, is_train, 'bn')
            if use_ln:
                print('ln in the {} layer'.format('share_bottom_layer_' + str(i)))
                o1, normalized, gamma, std = self.layer_norm(o1)
            o = tf.nn.relu(o1)
        else:
            o = o1

        return o, o1, w, normalized, gamma, std, input

    def layer_norm(self, inputs, epsilon=1e-12, scope="layer_norm"):
        with tf.variable_scope(scope, reuse=tf.AUTO_REUSE):
            inputs_shape = inputs.get_shape()
            params_shape = inputs_shape[-1:]
            mean, variance = tf.nn.moments(inputs, [-1], keep_dims=True)
            beta = tf.get_variable("beta", params_shape, initializer=tf.zeros_initializer())
            gamma = tf.get_variable("gamma", params_shape, initializer=tf.ones_initializer())
            std = ((variance + epsilon) ** (0.5))
            normalized = (inputs - mean) / std
            outputs = gamma * normalized + beta
        return outputs, normalized, gamma, std

    def fc_128(self, _dnn_net_size, input, input_size, layer_size, i, is_train, use_bn, use_ln):
        weight_name = 'w'
        w = tf.get_variable(weight_name, [input_size, layer_size],
                            initializer=tf.random_normal_initializer(
                                stddev=1.0 / math.sqrt(float(input_size))),
                            trainable=True)
        bias_name = 'b'
        b = tf.get_variable(bias_name, [layer_size],
                            initializer=tf.zeros_initializer,
                            trainable=True)
        print("%s length=%d * %d" % (w.name, input_size, layer_size))
        print("%s length=%d" % (b.name, layer_size))

        o1 = tf.add(tf.matmul(input, w), b)
        if i != len(_dnn_net_size) - 1:
            if use_bn:
                print('bn in the {} layer'.format('share_bottom_layer_' + str(i)))
                o1 = BatchNorm(o1, is_train, 'bn')
            if use_ln:
                print('ln in the {} layer'.format('share_bottom_layer_' + str(i)))
                o1 = tf.contrib.layers.layer_norm(o1)
            o = tf.nn.relu(o1)
        else:
            o = o1

        return o, w

    def fc1(self, input, input_size, layer_size, is_output_layer, is_train, dropout_rate, use_bn, use_ln, weights=None):
        weight_name = 'w'
        w = tf.get_variable(weight_name, [input_size, layer_size],
                            initializer=tf.random_normal_initializer(
                                stddev=1.0 / math.sqrt(float(input_size))),
                            trainable=True)
        bias_name = 'b'
        b = tf.get_variable(bias_name, [layer_size],
                            initializer=tf.zeros_initializer,
                            trainable=True)

        if is_output_layer:
            o = tf.add(tf.matmul(input, w), b)
        else:
            o = tf.nn.relu(tf.add(tf.matmul(input, w), b))
            if use_bn:
                o = kai.batch_norm(o, batch_weights=weights)
            if use_ln:
                o = tf.contrib.layers.layer_norm(o)
                # o = KaiLayerNorm(o)
            o = tf.layers.dropout(o, dropout_rate, training=is_train)
        return layer_size, o

    def fc1_tanh(self, input, input_size, layer_size, is_output_layer, is_train, dropout_rate, use_bn, use_ln, weights=None):
        weight_name = 'w'
        w = tf.get_variable(weight_name, [input_size, layer_size],
                            initializer=tf.random_normal_initializer(
                                stddev=1.0 / math.sqrt(float(input_size))),
                            trainable=True)
        bias_name = 'b'
        b = tf.get_variable(bias_name, [layer_size],
                            initializer=tf.zeros_initializer,
                            trainable=True)

        if is_output_layer:
            o = tf.add(tf.matmul(input, w), b)
        else:
            o = tf.nn.tanh(tf.add(tf.matmul(input, w), b))
            if use_bn:
                o = kai.batch_norm(o, batch_weights=weights)
            if use_ln:
                o = tf.contrib.layers.layer_norm(o)
            o = tf.layers.dropout(o, dropout_rate, training=is_train)
        return layer_size, o

    def senet_layer(self, embedding_matrix, field_size, pool_op, is_train, importance_fuc, dropout_rate, use_bn,
                    use_ln):
        with tf.variable_scope('SENET_layer', reuse=tf.AUTO_REUSE):
            with tf.variable_scope('pooling', reuse=tf.AUTO_REUSE):
                if pool_op == 'max':
                    z = tf.reduce_max(embedding_matrix, axis=2)  # batch * field_size * emb_size -> batch * field_size
                else:
                    z = tf.reduce_mean(embedding_matrix, axis=2)

            # excitation learn the weight of each field from above scaler
            with tf.variable_scope('excitation_layer_1', reuse=tf.AUTO_REUSE):
                _, z1 = self.fc1(z, field_size, 45, False, is_train, dropout_rate, use_bn, use_ln)
            with tf.variable_scope('excitation_layer_2', reuse=tf.AUTO_REUSE):
                _, a = self.fc1(z1, 45, field_size, True,
                                is_train, dropout_rate, use_bn, use_ln)  # batch * field_size
                if importance_fuc == 'sigmoid':
                    a = tf.sigmoid(a, name='emb_importance')
                elif importance_fuc == 'relu':
                    a = tf.nn.relu(a, name='emb_importance')

            # re-weight embedding with weight
            with tf.variable_scope('reweight_layer', reuse=tf.AUTO_REUSE):
                senet_embedding = tf.multiply(embedding_matrix, tf.reshape(a, (
                    -1, field_size, 1)))  # (batch * field * emb) * ( batch * field * 1)
            return senet_embedding

    def cross_layer(self, sparse_input, user_feature_size, photo_feature_size, combine_feature_size, embedding_size):
        user_units = user_feature_size * embedding_size
        photo_units = photo_feature_size * embedding_size
        combine_units = combine_feature_size * embedding_size

        cross_units = (user_feature_size * photo_feature_size) * embedding_size

        embedding_user = tf.slice(sparse_input, [0, 0], [-1, user_units])
        embedding_photo = tf.slice(sparse_input, [0, user_units], [-1, photo_units])
        embedding_combine = tf.slice(sparse_input, [0, user_units + photo_units], [-1, combine_units])
        #  code from zhaolei
        embedding_user_rs = tf.reshape(embedding_user,
                                       [-1, user_feature_size, 4, 4])  # ? * user_fea_size * 4 * 4
        embedding_user_rs_trans = tf.transpose(embedding_user_rs, perm=[0, 2, 1, 3])  # ? * 4 * user_fea_size * 4
        embedding_photo_rs = tf.reshape(embedding_photo,
                                        [-1, photo_feature_size, 4, 4])  # ? * photo_fea_size * 4 * 4
        embedding_photo_rs_trans = tf.transpose(embedding_photo_rs, perm=[0, 2, 3, 1])  # ? * 4 * 4 * photo_fea_size
        embedding_cross_sum = tf.matmul(embedding_user_rs_trans,
                                        embedding_photo_rs_trans)  # ? * 4 * user_fea_size * photo_fea_size
        embedding_cross = tf.reshape(embedding_cross_sum, [-1, cross_units // 4])

        return embedding_cross, embedding_combine

    def fc_v2(self, _dnn_net_size, input, input_size, layer_size, i, is_train, use_bn, use_ln):
        weight_name = 'w'
        w = tf.get_variable(weight_name, [input_size, layer_size],
                            initializer=tf.random_normal_initializer(
                                stddev=1.0 / math.sqrt(float(input_size))),
                            trainable=True)
        bias_name = 'b'
        b = tf.get_variable(bias_name, [layer_size],
                            initializer=tf.zeros_initializer,
                            trainable=True)
        print("%s length=%d * %d" % (w.name, input_size, layer_size))
        print("%s length=%d" % (b.name, layer_size))

        o1 = tf.add(tf.matmul(input, w), b)
        if i != len(_dnn_net_size) - 1:
            if use_bn:
                print('bn in the {} layer'.format('share_bottom_layer_' + str(i)))
                o1 = kai.batch_norm(o1, is_train, 'bn')
            if use_ln:
                print('ln in the {} layer'.format('share_bottom_layer_' + str(i)))
                o1 = tf.contrib.layers.layer_norm(o1)
                # layer_norm = tf.keras.layers.LayerNormalization(name="LayerNorm")
                # o1 = self.layer_norm(o1)
            o = tf.nn.leaky_relu(o1)
        else:
            o = o1

        return o

    def get_convex_input(self, sparse_input, sparse_size, embedding_size):
        sparse_units = sparse_size * embedding_size
        sparse_input_reshape = tf.reshape(sparse_input, [-1, sparse_size, embedding_size])
        topk = int(sparse_size * 0.6)
        with tf.variable_scope("fea_select_layer", reuse=tf.AUTO_REUSE):
            sparse_input = tf.layers.dropout(sparse_input, 0.5, training=tf.cast(self.is_train_pl, tf.bool))

            w1 = tf.get_variable('w1', [sparse_units, sparse_size * 4],
                                 initializer=tf.random_normal_initializer(
                                     stddev=1.0 / math.sqrt(float(sparse_units))),
                                 trainable=False)
            b1 = tf.get_variable('b1', [sparse_size * 4],
                                 initializer=tf.zeros_initializer,
                                 trainable=False)
            o1 = tf.add(tf.matmul(sparse_input, w1), b1)
            o1 = kai.batch_norm(o1, 'bn1')
            o1 = tf.nn.relu(o1)
            o1 = tf.layers.dropout(o1, 0.5, training=tf.cast(self.is_train_pl, tf.bool))
            w2 = tf.get_variable('w2', [sparse_size * 4, sparse_size * 4],
                                 initializer=tf.random_normal_initializer(
                                     stddev=1.0 / math.sqrt(float(sparse_size * 4))),
                                 trainable=False)
            b2 = tf.get_variable('b2', [sparse_size * 4],
                                 initializer=tf.zeros_initializer,
                                 trainable=False)
            o2 = tf.add(tf.matmul(o1, w2), b2)
            o2 = kai.batch_norm(o2, 'bn2')
            o2 = tf.nn.relu(o2)
            w3 = tf.get_variable('w3', [sparse_size * 4, sparse_size],
                                 initializer=tf.random_normal_initializer(
                                     stddev=1.0 / math.sqrt(float(sparse_size * 4))),
                                 trainable=False)
            b3 = tf.get_variable('b3', [sparse_size],
                                 initializer=tf.zeros_initializer,
                                 trainable=False)
            o = tf.add(tf.matmul(o2, w3), b3)
        loss_o = 0 * tf.reduce_sum(o)
        values1, indices_tpk = tf.nn.top_k(o, topk, name="topk")
        values2, indices_tpk_neg = tf.nn.top_k(-o, topk, name="topk_neg")
        # print("selected_fea shape:", selected_fea.get_shape().as_list())
        one_index1 = tf.reduce_sum(tf.one_hot(indices_tpk, sparse_size), 1)
        one_index2 = tf.reduce_sum(tf.one_hot(indices_tpk_neg, sparse_size), 1)

        dnn_input_1 = tf.reshape(tf.multiply(sparse_input_reshape, tf.reshape(one_index1, [-1, sparse_size, 1])),
                                 [-1, sparse_units])
        dnn_input_2 = tf.reshape(tf.multiply(sparse_input_reshape, tf.reshape(one_index2, [-1, sparse_size, 1])),
                                 [-1, sparse_units])
        return dnn_input_1, dnn_input_2, loss_o

    def convex_net(self, dnn_input, user_feature_size, photo_feature_size,
                   combine_feature_size, embedding_size, sample_weight, n_samples, label, name,generalize_input,generalize_input_size):
        with tf.variable_scope("convex_net_" + name, reuse=tf.AUTO_REUSE):
            input_size = dnn_input.get_shape().as_list()[1]

            sparse_size = user_feature_size + photo_feature_size + combine_feature_size
            sparse_units = sparse_size * embedding_size
            sparse_input = tf.slice(dnn_input, [0, 0], [-1, sparse_units])
            dense_input = tf.slice(dnn_input, [0, sparse_units], [-1, input_size - sparse_units])

            # add_feas = tf.slice(dnn_input, [0, sparse_units + dense_units], [-1, 20 * 16])
            sparse_input_reshape = tf.reshape(sparse_input, [-1, sparse_size, embedding_size])
            se_pool = "mean"
            se_act = "sigmoid"
            dropout_rate = 0.0

            senet_embedding = self.senet_layer(sparse_input_reshape, sparse_size, se_pool,
                                               self.is_train_pl, se_act, dropout_rate, False, True)

            senet_input = tf.reshape(senet_embedding, [-1, sparse_units])

            cross_input, embedding_combine = self.cross_layer(senet_input, user_feature_size,
                                                              photo_feature_size,
                                                              combine_feature_size,
                                                              embedding_size)

            dnn_input = tf.concat([cross_input, embedding_combine, dense_input], 1)
            input_size = dnn_input.get_shape().as_list()[1]
            input = dnn_input
            # generalize_input_size = generalize_input.get_shape().as_list()[1]
            for i in range(self.share_num):
                with tf.variable_scope("share_bottom_layer_new_{}".format(i), reuse=tf.AUTO_REUSE):
                    layer_size = self._dnn_net_size[i]
                    input = self.fc_v2(self._dnn_net_size, input, input_size, layer_size, i, self.is_train_pl, False, True)
                    input_size = layer_size
            input_size = input_size
            input = tf.concat([input, tf.stop_gradient(generalize_input)], -1)
            input_size = input_size + generalize_input_size
            for i in range(self.share_num, len(self._dnn_net_size)):
                with tf.variable_scope("upper_layer_{}".format(i), reuse=tf.AUTO_REUSE):
                    layer_size = self._dnn_net_size[i]
                    input = self.fc_v2(self._dnn_net_size, input, input_size, layer_size, i, self.is_train_pl, False,
                                       True)
                    input_size = layer_size
                    # input = tf.Print(input, [input], message=name + str(i) + ' converted input: ', first_n=3,
                    #                  summarize=2000)

            prob = tf.nn.softmax(input, name="softmax_" + name)
            cross_entropy = tf.divide(
                tf.reduce_sum(sample_weight * tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label,
                                                                                             logits=input)),
                n_samples, )
            return cross_entropy, input, prob


    def cross_layer_new(self, sparse_input, user_feature_size, photo_feature_size, combine_feature_size, embedding_size):
        #start = time.time()
        user_units = user_feature_size * embedding_size
        photo_units = photo_feature_size * embedding_size
        combine_units = combine_feature_size * embedding_size

        cross_units = (user_feature_size * photo_feature_size)

        embedding_user = tf.slice(sparse_input, [0, 0], [-1, user_units])
        embedding_photo = tf.slice(sparse_input, [0, user_units], [-1, photo_units])
        embedding_combine = tf.slice(sparse_input, [0, user_units + photo_units], [-1, combine_units])
        #  code from zhaolei
        embedding_user_rs = tf.reshape(embedding_user,
                                       [-1, user_feature_size, 4, 4])  # ? * user_fea_size * 4 * 4
        embedding_user_rs_trans = tf.transpose(embedding_user_rs, perm=[0, 2, 1, 3])  # ? * 4 * user_fea_size * 4
        embedding_photo_rs = tf.reshape(embedding_photo,
                                        [-1, photo_feature_size, 4, 4])  # ? * photo_fea_size * 4 * 4
        embedding_photo_rs_trans = tf.transpose(embedding_photo_rs, perm=[0, 2, 3, 1])  # ? * 4 * 4 * photo_fea_size
        embedding_cross_sum = tf.matmul(embedding_user_rs_trans,
                                        embedding_photo_rs_trans)  # ? * 4 * user_fea_size * photo_fea_size
        #embedding_cross_sum = tf.reshape(embedding_cross_sum, [-1, 4, user_feature_size * photo_feature_size])

        with tf.variable_scope('Cross_layer_sub', reuse=tf.AUTO_REUSE):
            input_size = 4 * user_feature_size * photo_feature_size
            weight_name1 = 'w_sub'
            w_sub = tf.get_variable(weight_name1, [4, user_feature_size, photo_feature_size],
                                initializer=tf.random_normal_initializer(
                                stddev=1.0 / math.sqrt(float(input_size))),
                                trainable=True)
            embedding_cross_old = tf.multiply(embedding_cross_sum, w_sub) # ? * 4 * user_fea_size * photo_fea_size
            embedding_cross = tf.reduce_sum(embedding_cross_old, axis = 1)           # ? * 1 * user_fea_size * photo_fea_size
            embedding_cross = tf.reshape(embedding_cross, [-1, cross_units])    # ? * (user_fea_size * photo_fea_size)
        return embedding_cross, embedding_combine

    def transformer_component(self, query_input, action_list_input, col, name, nh=8, att_emb_size=32):
        action_item_size = action_list_input.get_shape().as_list()[-1]
        Q = tf.get_variable(name + 'q_trans_matrix', (col, att_emb_size * nh))  # [emb, att_emb * hn]
        K = tf.get_variable(name + 'k_trans_matrix', (action_item_size, att_emb_size * nh))
        V = tf.get_variable(name + 'v_trans_matrix', (action_item_size, att_emb_size * nh))
        querys = tf.tensordot(query_input, Q, axes=(-1, 0))  # (batch_size,sq_q,att_embedding_size*head_num)
        keys = tf.tensordot(action_list_input, K, axes=(-1, 0))
        values = tf.tensordot(action_list_input, V, axes=(-1, 0))  # (batch_size,sq_v,att_embedding_size*head_num)

        querys = tf.stack(tf.split(querys, nh, axis=2))  # (head_num,batch_size,field_sizeq,att_embedding_size)
        keys = tf.stack(tf.split(keys, nh, axis=2))  # (head_num,batch_size,field_sizek,att_embedding_size)
        values = tf.stack(tf.split(values, nh, axis=2))  # (head_num,batch_size,field_sizev,att_embedding_size)

        inner_product = tf.matmul(querys, keys, transpose_b=True) / 8.0  # (head_num,batch_size,field_sizeq,field_sizek)
        normalized_att_scores = tf.nn.softmax(inner_product, name="sotfmax_trans")  # (head_num,batch_size,field_sizeq,field_sizek)
        result = tf.matmul(normalized_att_scores, values)  # (head_num,batch_size,field_sizeq,att_embedding_sizev)
        result = tf.transpose(result, perm=[1, 2, 0, 3])  # (batch_size,field_sizeq,hn, att_embedding_sizev)
        mha_result = tf.reshape(result, (-1, nh * att_emb_size))
        return mha_result

    def transformer_component_v2(self,query_input, action_list_input, col, name, nh=1, att_emb_size=32):
        assert nh == 1
        action_item_size = action_list_input.get_shape().as_list()[-1]
        Q = tf.get_variable(name + 'q_trans_matrix', (col, att_emb_size * nh))  # [emb, att_emb * hn]
        K = tf.get_variable(name + 'k_trans_matrix', (action_item_size, att_emb_size * nh))
        V = tf.get_variable(name + 'v_trans_matrix', (action_item_size, att_emb_size * nh))
        querys = tf.matmul(query_input, Q)  # (batch_size,sq_q,att_embedding_size*head_num)
        keys = tf.matmul(action_list_input, K)
        values = tf.matmul(action_list_input, V)  # (batch_size,sq_v,att_embedding_size*head_num)

        #querys = tf.stack(tf.split(querys, nh, axis=2))  # (head_num,batch_size,field_sizeq,att_embedding_size)
        #keys = tf.stack(tf.split(keys, nh, axis=2))  # (head_num,batch_size,field_sizek,att_embedding_size)
        #values = tf.stack(tf.split(values, nh, axis=2))  # (head_num,batch_size,field_sizev,att_embedding_size)
        querys = tf.reshape(querys, [-1, 1, att_emb_size])
        inner_product = tf.matmul(querys, keys, transpose_b=True) / 8.0  # (head_num,batch_size,field_sizeq,field_sizek)
        normalized_att_scores = tf.nn.softmax(inner_product, name="sotfmax_trans")  # (head_num,batch_size,field_sizeq,field_sizek)
        result = tf.matmul(normalized_att_scores, values)  # (head_num,batch_size,field_sizeq,att_embedding_sizev)
        mha_result = tf.reshape(result, [-1, nh * att_emb_size])
        return mha_result
    
    def my_matmul(self, a, b):
        bb = tf.transpose(b, [0, 2, 1])
        a_size_list = a.get_shape().as_list()
        bb_size_list = bb.get_shape().as_list()
        c = tf.reduce_sum(tf.multiply(tf.reshape(a, [-1, a_size_list[1], 1, a_size_list[2]]),
                                      tf.reshape(bb, [-1, 1, bb_size_list[1], bb_size_list[2]])),
                          axis=-1)
        return c

    def ssl_dnn(self, dnn_input, dnn_input_size, dnn_net_size, name):
        input = dnn_input
        input_size = dnn_input_size
        for i in range(len(dnn_net_size)):
            with tf.variable_scope(name + str(i), reuse=tf.AUTO_REUSE):
                layer_size = dnn_net_size[i]
                w = tf.get_variable('w', [input_size, layer_size],
                                    initializer=tf.random_normal_initializer(
                                        stddev=1.0 / math.sqrt(float(dnn_input_size))),
                                    trainable=True)
                b = tf.get_variable('b', [layer_size], initializer=tf.zeros_initializer, trainable=True)
                o = tf.add(tf.matmul(input, w), b)
                if i != len(dnn_net_size) - 1:
                    o = tf.nn.relu(o)
                input_size = layer_size
                input = o
            input_size = dnn_net_size[i]
            input = o
        return input_size, input

    def interpret_dnn(self, dnn_input, dnn_input_size, dnn_net_size, name):
        input = dnn_input
        input_list = []
        input_size = dnn_input_size
        for i in range(len(dnn_net_size)):
            with tf.variable_scope(name + str(i), reuse=tf.AUTO_REUSE):
                layer_size = dnn_net_size[i]
                w = tf.get_variable('w', [input_size, layer_size],
                                    initializer=tf.random_normal_initializer(
                                        stddev=1.0 / math.sqrt(float(dnn_input_size))),
                                    trainable=True)
                b = tf.get_variable('b', [layer_size], initializer=tf.zeros_initializer, trainable=True)
                o = tf.add(tf.matmul(input, w), b)
                if i != len(dnn_net_size) - 1:
                    o = tf.nn.relu(o)
                input_size = layer_size
                input = o
                input_list.append(input)
            input_size = dnn_net_size[i]
        return input_list

    def ssl_tower(self, seq, mask, scope_name):
        with tf.name_scope("ssl_tower"):
            ssl_embedding = seq
            input_size = ssl_embedding.get_shape().as_list()[1]
            shared_input_size, ssl_tower_emb = self.ssl_dnn(ssl_embedding, input_size, ssl_net_sizes, scope_name)
            ssl_tower_emb_2 = tf.nn.l2_normalize(ssl_tower_emb, -1)
            ssl_tower_emb = tf.contrib.layers.layer_norm(tf.stop_gradient(ssl_tower_emb)) * 0.0

            return ssl_tower_emb + ssl_tower_emb_2

    def mask_and_dropout(self, seq_emb, max_seq_len, embedding_size):
        with tf.name_scope("mask_and_dropout"):
            ones_mask_matrix = tf.ones([1, max_seq_len, 1])
            zero_mask_matrix = tf.zeros([1, max_seq_len, 1])
            dropout_matrix = tf.nn.dropout(ones_mask_matrix, rate = 0.5, seed = 1)
            cond = tf.greater(dropout_matrix, 0.0)
            part_one_mask_matrix = tf.where(cond, ones_mask_matrix, zero_mask_matrix)
            part_two_mask_matrix = tf.where(cond, zero_mask_matrix, ones_mask_matrix)
            seq = tf.reshape(seq_emb, [-1, max_seq_len, embedding_size])
            part_one_fake_seq = tf.multiply(seq, tf.cast(part_one_mask_matrix, dtype=tf.float32))
            part_two_fake_seq = tf.multiply(seq, tf.cast(part_two_mask_matrix, dtype=tf.float32))
            return tf.layers.flatten(part_one_fake_seq), tf.layers.flatten(part_two_fake_seq)

    def get_cl_loss(self, fake_embedding1, fake_embedding2,alpha=0.0001,temp=5.0):
        cross_dot = tf.matmul(fake_embedding1, fake_embedding2, transpose_b=True) # [Batch_size, Batch_size]
        tao=1.0/temp
        cross_dot = cross_dot*tao
        softmax_logits = tf.nn.softmax(cross_dot, axis = -1, name="cl_softmax") + 1e-8
        loss = -1*alpha*tf.reduce_sum(tf.diag_part(tf.log(softmax_logits)))
        return loss

    def get_cl_loss_opt(self, fake_embedding1, fake_embedding2,flag=1, alpha=0.0001):
        def opt(A, a, b, beta=1.0):
            if beta is not None:
                A = tf.exp(-1.0*A/beta)
            a = tf.reshape(a,[-1, 1])
            b = tf.reshape(b,[-1, 1])
            v = tf.diag_part(tf.ones_like(A))
            v = tf.reshape(v, [-1, 1])
            for _ in range(5):
                u = a / tf.matmul(A, v)
                v = b / tf.matmul(tf.transpose(A,[1,0]), u)
            pi = tf.matmul(tf.diag(tf.reshape(u,[-1])), A)
            pi = tf.matmul(pi, tf.diag(tf.reshape(v,[-1])))
            return pi
        cross_dot = 1.0 - tf.matmul(fake_embedding1, fake_embedding2, transpose_b=True) # [Batch_size, Batch_size] cosine distance
        A = cross_dot
        if flag == 1:
            a = tf.reduce_sum(A,axis=0)
            a = a/(tf.reduce_sum(a)+1e-6)
            b = tf.reduce_sum(A,axis=1)
            b = b/(tf.reduce_sum(b)+1e-6)
        elif flag == 2:
            a = tf.diag_part(tf.ones_like(A))
            a = a/(tf.reduce_sum(a)+1e-6)
            b = tf.diag_part(tf.ones_like(A))
            b = a/(tf.reduce_sum(b)+1e-6)
        pi = opt(A, a, b)
        sum_val = tf.reduce_sum(tf.multiply(A,pi))
        loss = alpha*sum_val
        return loss

    def get_slice(self, input, index, embedding_size):
        part = tf.slice(input, [0, index*embedding_size], [-1, embedding_size])
        return part

    def interpret_part(self, user_embedding, photo_embedding, item_embedding):
        u_p_input = tf.concat([user_embedding, photo_embedding], axis=-1)
        u_i_input = tf.concat([user_embedding, item_embedding], axis=-1)
        u_p_output  = self.interpret_dnn(u_p_input, 32, interpret_sizes_1, name="u_p_input")
        u_i_output  = self.interpret_dnn(u_i_input, 32, interpret_sizes_1, name="u_i_input")
        u_p_prob, u_p_emb = u_p_output[-1], u_p_output[-2]
        u_i_prob, u_i_emb = u_i_output[-1], u_i_output[-2]
        u_p_i_input = tf.concat([u_p_emb, u_i_emb], axis=-1)
        return u_p_prob, u_i_prob, u_p_i_input

    def feature_mask(self, embedding, sample_mask, feature_id, num):
        mask = []
        for i in range(num):
            if i not in feature_id:
                mask.extend([1.0] * 16)
            else:
                mask.extend([0.0] * 16)
        mask = tf.constant(mask)
        embedding = (1.0 - sample_mask) * embedding + sample_mask * (embedding * mask)
        return embedding

    def kai_v2_model_def(self):
        from kai.tensorflow.nn import ParamAttr

        is_cpu_ps = False
        is_train = True
        hooks = []
        exclude_dense = []
        default_param_attr = ParamAttr(initializer=kai.nn.UniformInitializer(0.01),
                                    access_method=kai.nn.ProbabilityAccess(100),
                                    recycle_method=kai.nn.UnseendaysRecycle(3650000, 0.0, False))
        kai.nn.set_default_param_attr(default_param_attr)

        # sparse user coupon fea
        user_coupon = kai.new_embedding('user_coupon', dim=16, slots=self.user_coupon_fea_slots, expand=None)

        sparse0 = kai.new_embedding('sparse0', dim=16, slots=[1, 2, 3, 4], expand=None)  # 4
        sparse1 = kai.new_embedding('sparse1', dim=16, slots=[6, 7, 8, 9, 10, 11], expand=None)  # 6
        sparse2 = kai.new_embedding('sparse2', dim=16, slots=[13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 
                                                              31, 32, 33, 34, 35, 36, 37, 38, 40, 42, 43, 44, 45, 46, 47, 48, 49, 50, 
                                                              51, 52, 53, 54, 55, 56], expand=None)  # 42
        sparse3 = kai.new_embedding('sparse3', dim=16, slots=[59, 60, 61], expand=None)  # 3
        sparse4 = kai.new_embedding('sparse4', dim=16, slots=[66, 67, 68, 69, 70, 71, 72, 73, 74], expand=None)  # 9
        sparse5 = kai.new_embedding('sparse5', dim=16, slots=[76, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89], expand=None)  # 12
        sparse6 = kai.new_embedding('sparse6', dim=16, slots=[91, 93, 96, 97, 99, 105, 106, 107, 108, 109, 115], expand=None)  # 11
        sparse7 = kai.new_embedding('sparse7', dim=16, slots=[121, 122, 123, 124, 125, 127, 128, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159,
                                                            160, 161, 162, 167, 168, 169, 170, 171, 172, 173, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258,
                                                            259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 700, 100, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206,
                                                            207, 208, 209, 210, 211, 212, 213, 218, 219, 221, 222, 223, 224, 723, 39, 41, 63, 64, 65, 77, 78, 92, 94, 95, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241,
                                                            242, 243, 244, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299], expand=None)  # 177
        sparse8 = kai.new_embedding('sparse8', dim=16, slots=[101, 102, 103, 104, 110, 111,
                                                              112, 113, 114, 116, 117, 126, 129, 163, 164, 165, 166], expand=None)  # 17
        sparse9 = kai.new_embedding('sparse9', dim=16, slots=[320, 321, 322, 323, 324, 325, 326, 327, 
                                                              300, 301, 302, 303, 304, 305, 306, 307, 308, 309,
                                                              310, 311, 312, 313, 314, 315, 316, 317], expand=None)  # 26
        sparse10 = kai.new_embedding('sparse10', dim=16, slots=list(range(390,410)), expand=None)  # 20
        sparse11 = kai.new_embedding('sparse11', dim=16, slots=[701,331,702,333,703,335,704,337,705,339,706,351,707,353,708,355,709,357,710,359], expand=None)  # 20
        sparse12 = kai.new_embedding('sparse12', dim=16, slots=[370,372,373,374]+[724,411,412,725,414,415,416,417,726,419,420,421,422], expand=None)  # 17
        sparse13 = kai.new_embedding('sparse13', dim=16, slots=[i for i in range(423, 428)], expand=None)  # 5
        sparse14 = kai.new_embedding('sparse14', dim=16, slots=[i for i in range(428, 435)], expand=None)  # 7
        price_range_fea = kai.new_embedding('sparse15', dim=16, slots=[471, 472, 473], expand=None)  # 3
        SIM_G_SEQ_16_380 = kai.new_embedding('SIM_G_SEQ_16_380', dim=16, slots=list(range(380, 381)), expand=100)  # 1 
        SIM_G_SEQ_16_381 = kai.new_embedding('SIM_G_SEQ_16_381', dim=16, slots=list(range(381, 382)), expand=100)  # 1
        SIM_G_SEQ_8_382 = kai.new_embedding('SIM_G_SEQ_8_382', dim=8, slots=list(range(382, 383)), expand=100)  # 1
        SIM_G_SEQ_8_383 = kai.new_embedding('SIM_G_SEQ_8_383', dim=8, slots=list(range(383, 384)), expand=100)  # 1
        SIM_G_SEQ_8_384 = kai.new_embedding('SIM_G_SEQ_8_384', dim=8, slots=list(range(384, 385)), expand=100)  # 1
        akg_user_361 = kai.new_embedding('akg_user_361', dim=8, slots=list(range(361, 362)), expand=40)  # 1
        akg_user_362 = kai.new_embedding('akg_user_362', dim=8, slots=list(range(362, 363)), expand=40)  # 1
        akg_user_367 = kai.new_embedding('akg_user_367', dim=8, slots=list(range(367, 368)), expand=20)  # 1
        akg_user_368 = kai.new_embedding('akg_user_368', dim=8, slots=list(range(368, 369)), expand=100)  # 1
        akg_combine_371 = kai.new_embedding('akg_combine_371', dim=8, slots=list(range(371, 372)), expand=20)  # 1
        cot_user = kai.new_embedding('cot_user', dim=16, slots=[i for i in range(435, 459)], expand=None)  # 24
        cot_photo = kai.new_embedding('cot_photo', dim=16, slots=[i for i in range(459, 471)], expand=None)  # 12
        selftrain_debias = kai.new_embedding('selftrain_debias', dim=16, slots=list(range(498, 499)), expand=None)  # 1
        ExtractURealTimeMerchantOrderPaiedWholePayStatDense = kai.get_dense_fea('ExtractURealTimeMerchantOrderPaiedWholePayStatDense', dim=16)
        ExtractURealTimeMerchantOrderPaiedPhotoPayStatDense = kai.get_dense_fea('ExtractURealTimeMerchantOrderPaiedPhotoPayStatDense', dim=16)
        ExtractURealTimeMerchantOrderPaiedLivePayStatDense = kai.get_dense_fea('ExtractURealTimeMerchantOrderPaiedLivePayStatDense', dim=16)
        ExtractUserAdLpsNumExtendEcomDenseV2 = kai.get_dense_fea('ExtractUserAdLpsNumExtendEcomDenseV2', dim=2)
        ExtractUserEcomTextFeatureCtrConv = kai.get_dense_fea('ExtractUserEcomTextFeatureCtrConv', dim=32)
        ExtractUserAdItemClickNumExtendEcomDenseV2 = kai.get_dense_fea('ExtractUserAdItemClickNumExtendEcomDenseV2', dim=2)
        ExtractRecoSlideFmRe = kai.get_dense_fea('ExtractRecoSlideFmRe', dim=128)
        ExtractUserEcomItemImpr7dDense = kai.get_dense_fea('ExtractUserEcomItemImpr7dDense', dim=1)
        ExtractDenseUniverseFlagNew = kai.get_dense_fea('ExtractDenseUniverseFlagNew', dim=1)
        ExtractGaussDropoutDense = kai.get_dense_fea('ExtractGaussDropoutDense', dim=256)
        ExtractUserEshopEmbeddingV3 = kai.get_dense_fea('ExtractUserEshopEmbeddingV3', dim=64)
        ExtractItemEshopEmbeddingSpu = kai.get_dense_fea('ExtractItemEshopEmbeddingSpu', dim=64)
        ExtractPhotoEshopEmbeddingV3 = kai.get_dense_fea('ExtractPhotoEshopEmbeddingV3', dim=64)
        ExtractAdLpsPosterRate = kai.get_dense_fea('ExtractAdLpsPosterRate', dim=12)
        ExtractPhotoEmbeddingFeature2 = kai.get_dense_fea('ExtractPhotoEmbeddingFeature2', dim=64)
        ExtractPhotoSharkEmbedding = kai.get_dense_fea('ExtractPhotoSharkEmbedding', dim=64)
        ExtractItemSharkEmbedding = kai.get_dense_fea('ExtractItemSharkEmbedding', dim=64)
        ExtractItemDenseEcomOcpxActionType = kai.get_dense_fea('ExtractItemDenseEcomOcpxActionType', dim=3)
        ExtractNumDense = kai.get_dense_fea('ExtractNumDense', dim=16)
        ExtractMatchDenseNum14DayNew = kai.get_dense_fea('ExtractMatchDenseNum14DayNew', dim=15)
        ExtractCombineShorttermActionCntTsItemImp = kai.get_dense_fea('ExtractCombineShorttermActionCntTsItemImp', dim=66)
        ExtractCombineShorttermActionCntTsOrderPay = kai.get_dense_fea('ExtractCombineShorttermActionCntTsOrderPay', dim=66)
        ExtractCombineUserLongTermActionCntItemClk = kai.get_dense_fea('ExtractCombineUserLongTermActionCntItemClk', dim=66)
        ExtractCombineRealtimePhotoImpMatchCnt = kai.get_dense_fea('ExtractCombineRealtimePhotoImpMatchCnt', dim=24)
        ExtractCombineRealtimeItemImpMatchCnt = kai.get_dense_fea('ExtractCombineRealtimeItemImpMatchCnt', dim=24)
        ExtractCombineRealtimeP3sMatchCnt = kai.get_dense_fea('ExtractCombineRealtimeP3sMatchCnt', dim=24)
        ExtractCombineRealtimePedMatchCnt = kai.get_dense_fea('ExtractCombineRealtimePedMatchCnt', dim=24)
        ExtractCombineRealtimePageImpMatchCnt = kai.get_dense_fea('ExtractCombineRealtimePageImpMatchCnt', dim=24)
        ExtractCombineRealtimeFormSubmitMatchCnt = kai.get_dense_fea('ExtractCombineRealtimeFormSubmitMatchCnt', dim=24)
        ExtractCombineRealtimeCommentDialogMatchCnt = kai.get_dense_fea('ExtractCombineRealtimeCommentDialogMatchCnt', dim=24)
        ExtractCombineLongtermPhotoImpMatchCnt = kai.get_dense_fea('ExtractCombineLongtermPhotoImpMatchCnt', dim=27)
        ExtractCombineLongtermItemImpMatchCnt = kai.get_dense_fea('ExtractCombineLongtermItemImpMatchCnt', dim=27)
        ExtractCombineLongtermClickMatchCnt = kai.get_dense_fea('ExtractCombineLongtermClickMatchCnt', dim=27)
        ExtractCombineLongtermP3sMatchCnt = kai.get_dense_fea('ExtractCombineLongtermP3sMatchCnt', dim=27)
        ExtractCombineLongtermP5sMatchCnt = kai.get_dense_fea('ExtractCombineLongtermP5sMatchCnt', dim=27)
        ExtractCombineLongtermPedMatchCnt = kai.get_dense_fea('ExtractCombineLongtermPedMatchCnt', dim=27)
        ExtractCombineLongtermFormSubmitMatchCnt = kai.get_dense_fea('ExtractCombineLongtermFormSubmitMatchCnt', dim=27)
        ExtractCombineLongtermOrderSubmitMatchCnt = kai.get_dense_fea('ExtractCombineLongtermOrderSubmitMatchCnt', dim=27)
        ExtractCombineRealtimeMmuItemClickMatchCnt = kai.get_dense_fea('ExtractCombineRealtimeMmuItemClickMatchCnt', dim=3)
        ExtractCombineRealtimeMmuP5sMatchCnt = kai.get_dense_fea('ExtractCombineRealtimeMmuP5sMatchCnt', dim=3)
        ExtractCombineRealtimeMmuPedMatchCnt = kai.get_dense_fea('ExtractCombineRealtimeMmuPedMatchCnt', dim=3)
        ExtractCombineRealtimeNewAdPhotoPlayedEndMatchCnt = kai.get_dense_fea('ExtractCombineRealtimeNewAdPhotoPlayedEndMatchCnt', dim=32)
        ExtractCombineRealtimeNewAdPhotoPlayed3sMatchCnt = kai.get_dense_fea('ExtractCombineRealtimeNewAdPhotoPlayed3sMatchCnt', dim=32)
        ExtractCombineRealtimeNewAdLiveShopClickMatchCnt = kai.get_dense_fea('ExtractCombineRealtimeNewAdLiveShopClickMatchCnt', dim=32)
        ExtractCombineRealtimeNewEventGoodsViewMatchCnt = kai.get_dense_fea('ExtractCombineRealtimeNewEventGoodsViewMatchCnt', dim=32)
        ExtractCombineRealtimeNewAdItemClickMatchCnt = kai.get_dense_fea('ExtractCombineRealtimeNewAdItemClickMatchCnt', dim=32)
        ExtractCombineRealtimeNewEventOrderPaidMatchCnt = kai.get_dense_fea('ExtractCombineRealtimeNewEventOrderPaidMatchCnt', dim=32)
        ExtractCombineRealtimeNewAdItemImpressionMatchCnt = kai.get_dense_fea('ExtractCombineRealtimeNewAdItemImpressionMatchCnt', dim=32)
        ExtractCombineRealtimeNewAdLivePlayed1mMatchCnt = kai.get_dense_fea('ExtractCombineRealtimeNewAdLivePlayed1mMatchCnt', dim=32)
        ExtractCombineRealtimeNewAdLiveShopLinkJumpMatchCnt = kai.get_dense_fea('ExtractCombineRealtimeNewAdLiveShopLinkJumpMatchCnt', dim=32)
        ExtractRewardedIsShowOrderPaiedInspire = kai.get_dense_fea('ExtractRewardedIsShowOrderPaiedInspire', dim=1)
        ExtractURealTimeMerchantOrderPaiedMatchDense = kai.get_dense_fea('ExtractURealTimeMerchantOrderPaiedMatchDense', dim=24)
        ExtractURealTimeMerchantOrderSubmitMatchDense = kai.get_dense_fea('ExtractURealTimeMerchantOrderSubmitMatchDense', dim=24)
        ExtractURealTimeMerchantGoodsViewMatchDense = kai.get_dense_fea('ExtractURealTimeMerchantGoodsViewMatchDense', dim=24)
        ExtractURealTimeMerchantShopClickMatchDense = kai.get_dense_fea('ExtractURealTimeMerchantShopClickMatchDense', dim=24)
        ExtractURealTimeMerchantOrderSubmitClickMatchDense = kai.get_dense_fea('ExtractURealTimeMerchantOrderSubmitClickMatchDense', dim=24)
        ExtractURealTimeMerchantProductBuyClickMatchDense = kai.get_dense_fea('ExtractURealTimeMerchantProductBuyClickMatchDense', dim=24)
        ExtractURealTimeMerchantOrderImpressionMatchDense = kai.get_dense_fea('ExtractURealTimeMerchantOrderImpressionMatchDense', dim=24)
        ExtractItemDenseAccountOrientSelection = kai.get_dense_fea('ExtractItemDenseAccountOrientSelection', dim=1)
        ExtractItemDenseEcomStorewideRoas = kai.get_dense_fea('ExtractItemDenseEcomStorewideRoas', dim=1)
        ExtractUserDenseClickCotOpenEmb = kai.get_dense_fea('ExtractUserDenseClickCotOpenEmb', dim=64)
        ExtractUserDenseClickCotCloseEmb = kai.get_dense_fea('ExtractUserDenseClickCotCloseEmb', dim=64)
        ExtractUserDenseOrderCotOpenEmb = kai.get_dense_fea('ExtractUserDenseOrderCotOpenEmb', dim=64)
        ExtractUserDenseOrderCotCloseEmb = kai.get_dense_fea('ExtractUserDenseOrderCotCloseEmb', dim=64)
        ExtractPhotoDenseCotOpenEmb = kai.get_dense_fea('ExtractPhotoDenseCotOpenEmb', dim=64)
        ExtractPhotoDenseCotCloseEmb = kai.get_dense_fea('ExtractPhotoDenseCotCloseEmb', dim=64)

        ExtractUserDensePurchaseDegree = kai.get_dense_fea('ExtractUserDensePurchaseDegree', dim=216)
        ExtractItemDenseSkuMinPrice = kai.get_dense_fea('ExtractItemDenseSkuMinPrice', dim=1)
        ExtractItemDenseSkuMaxPrice = kai.get_dense_fea('ExtractItemDenseSkuMaxPrice', dim=1)
        ExtractItemDenseSkuMedianPrice = kai.get_dense_fea('ExtractItemDenseSkuMedianPrice', dim=1)
        sparse_feature = kai.get_sparse_fea(name='472')
        price_range = sparse_feature[0]

        dense = tf.concat([ExtractUserAdLpsNumExtendEcomDenseV2, ExtractUserEcomTextFeatureCtrConv, ExtractUserAdItemClickNumExtendEcomDenseV2, ExtractRecoSlideFmRe, ExtractUserEcomItemImpr7dDense, ExtractDenseUniverseFlagNew, ExtractGaussDropoutDense, ExtractUserEshopEmbeddingV3, ExtractItemEshopEmbeddingSpu, ExtractPhotoEshopEmbeddingV3, ExtractAdLpsPosterRate, ExtractPhotoEmbeddingFeature2, ExtractNumDense, ExtractMatchDenseNum14DayNew, ExtractCombineShorttermActionCntTsItemImp, ExtractCombineShorttermActionCntTsOrderPay, ExtractCombineUserLongTermActionCntItemClk, ExtractCombineRealtimePhotoImpMatchCnt, ExtractCombineRealtimeItemImpMatchCnt, ExtractCombineRealtimeP3sMatchCnt, ExtractCombineRealtimePedMatchCnt, ExtractCombineRealtimePageImpMatchCnt, ExtractCombineRealtimeFormSubmitMatchCnt, ExtractCombineRealtimeCommentDialogMatchCnt, ExtractCombineLongtermPhotoImpMatchCnt, ExtractCombineLongtermItemImpMatchCnt, ExtractCombineLongtermClickMatchCnt, ExtractCombineLongtermP3sMatchCnt, ExtractCombineLongtermP5sMatchCnt, ExtractCombineLongtermPedMatchCnt, ExtractCombineLongtermFormSubmitMatchCnt, ExtractCombineLongtermOrderSubmitMatchCnt, ExtractCombineRealtimeMmuItemClickMatchCnt, ExtractCombineRealtimeMmuP5sMatchCnt, ExtractCombineRealtimeMmuPedMatchCnt, ExtractCombineRealtimeNewAdPhotoPlayedEndMatchCnt, ExtractCombineRealtimeNewAdPhotoPlayed3sMatchCnt, ExtractCombineRealtimeNewAdLiveShopClickMatchCnt, ExtractCombineRealtimeNewEventGoodsViewMatchCnt, ExtractCombineRealtimeNewAdItemClickMatchCnt, ExtractCombineRealtimeNewEventOrderPaidMatchCnt, ExtractCombineRealtimeNewAdItemImpressionMatchCnt, ExtractCombineRealtimeNewAdLivePlayed1mMatchCnt, ExtractCombineRealtimeNewAdLiveShopLinkJumpMatchCnt, ExtractRewardedIsShowOrderPaiedInspire,ExtractItemDenseEcomOcpxActionType,ExtractURealTimeMerchantOrderPaiedWholePayStatDense,ExtractURealTimeMerchantOrderPaiedPhotoPayStatDense,ExtractURealTimeMerchantOrderPaiedLivePayStatDense,ExtractURealTimeMerchantOrderPaiedMatchDense,ExtractURealTimeMerchantOrderSubmitMatchDense,ExtractURealTimeMerchantGoodsViewMatchDense,ExtractURealTimeMerchantShopClickMatchDense,ExtractURealTimeMerchantOrderSubmitClickMatchDense,ExtractURealTimeMerchantProductBuyClickMatchDense,ExtractURealTimeMerchantOrderImpressionMatchDense, ExtractItemDenseAccountOrientSelection, ExtractItemDenseEcomStorewideRoas, ExtractUserDensePurchaseDegree, ExtractItemDenseSkuMinPrice, ExtractItemDenseSkuMaxPrice, ExtractItemDenseSkuMedianPrice, ], axis=1)
        cot_dense =tf.concat([ExtractUserDenseClickCotOpenEmb,ExtractUserDenseClickCotCloseEmb,ExtractUserDenseOrderCotOpenEmb,ExtractUserDenseOrderCotCloseEmb,ExtractPhotoDenseCotOpenEmb,ExtractPhotoDenseCotCloseEmb], axis=1)
        dnn_input = [
            sparse0, sparse1, sparse2, sparse3, sparse4, sparse5, sparse6, sparse7,sparse8, sparse9, sparse10,sparse11,sparse12, sparse13, sparse14, 
            SIM_G_SEQ_16_380, SIM_G_SEQ_16_381, SIM_G_SEQ_8_382, SIM_G_SEQ_8_383, SIM_G_SEQ_8_384, 
            akg_user_361, akg_user_362, akg_user_367, akg_user_368, akg_combine_371, ExtractPhotoSharkEmbedding, ExtractItemSharkEmbedding, 
            dense, cot_user, cot_photo, cot_dense, price_range_fea, price_range, 
            user_coupon,
            selftrain_debias
        ]
        labels = kai.get_label('92c8c0bef422b08ff1265b2ef8abbf34', from_sparse=True)
        labels = tf.cast(labels, tf.int32)
        #下面的block_data、 non_block_data只是为了完成兼容，用户可以自行修改
        block_data = [type('', (), dict(output=input))() for input in dnn_input]
        non_block_data = type('', (), dict(label_pl=labels))()
        with tf.xla.experimental.jit_scope():
            metric, eval_targets, prediction, _ = self.model_def(block_data, non_block_data)
        sparse_optimizer = kai.optimizer.AdagradW(learning_rate=0.05, eps=0.0001, decay=0.0, l2=0.0, version=2)
        dense_optimizer = kai.optimizer.AdagradW(learning_rate=0.05, eps=0.0001, decay=0.0, l2=0.0, version=2)
        sparse_optimizer.minimize(metric['loss'] * tf.cast(tf.shape(labels)[0], tf.float32) * kai.worker_num(), var_list=kai.get_collection(kai.GraphKeys.EMBEDDING_INPUT))
        dense_optimizer.minimize(metric['loss'] * tf.cast(tf.shape(labels)[0], tf.float32), var_list=kai.get_collection(kai.GraphKeys.TRAINABLE_VARIABLES))
        return {'optimizer': [sparse_optimizer, dense_optimizer], 'metrics': eval_targets}

    def model_def(self, block_data, non_block_data):

        self._dnn_net_size = [1024, 256, 128, 2]
        self.general_tower = [128, 64, 32, 2]
        self.qcpx_net_size = [256, 128, 1]
        self.user_coupon_net_size = [128, 64, 1]
        self.debias_net_size = [1152]
        self.share_num = 1
        embedding_size = 16
        use_bn = False
        ln = True
        senet_ratio = 4.0
        dropout_rate = 0.0
        set_t = True
        self.trainable_task_weight = True
        se_pool = "mean"
        se_act = "sigmoid"
        self.is_train_pl = False
        is_train = False
        self.neg_weight = 1.0
        labels = non_block_data.label_pl
        labels = tf.cast(labels, tf.int32)
        labels = tf.bitwise.bitwise_and(labels, (1 << 12) - 1) ## action labels
        labels = tf.reshape(labels, [-1])
        logger.info("origin_1 label shape: {}".format(labels.get_shape()))
        tbl = get_lookup_tbl_with_neg(11, self.neg_weight)
        labels = tf.gather(tbl, labels)
        logger.info("origin_2 label shape: {}".format(labels.get_shape()))

        label_ctcvr = tf.cast(labels[:, 2], tf.int32)
        label_ctr = tf.cast(labels[:, 1], tf.int32)
        label_pxr = tf.cast(labels[:, 3], tf.int32)
        label_playend = tf.cast(labels[:, 4], tf.int32)

        label_item = tf.cast(labels[:, 5], tf.int32)
        label_item_mask = tf.cast(labels[:, 6], tf.float32)
        label_photo = tf.cast(labels[:, 7], tf.int32)
        label_photo_mask = tf.cast(labels[:, 8], tf.float32)
        label_cid = tf.cast(labels[:, 10], tf.int32)

        sample_weight = labels[:, -1]
        n_samples = tf.reduce_sum(sample_weight)

        user_feature_size = 194
        photo_feature_size = 30
        combine_feature_size = 19 + 19
        add_user_fea_size = 2

        real_dense_fields = [2, 32, 2, 128, 1, 1, 64, 64, 64, 12, 64, 16, 15, 66, 66, 66, 24, 24, 24, 24, 24, 24, 24, 27, 27, 27, 27, 27, 27, 27, 27, 3, 3, 3, 32, 32, 32, 32, 32, 32, 32, 32, 32, 1]

        sparse_size = user_feature_size + photo_feature_size + combine_feature_size
        sparse_units = sparse_size * embedding_size
        print("sparse_units", sparse_units)
        dense_units = sum(real_dense_fields)
        print("dense_unist", dense_units)
        input_size = sparse_units + dense_units

        sparse_input_0 = block_data[0].output
        sparse_input_1 = block_data[1].output
        sparse_input_2 = block_data[2].output
        sparse_input_3 = block_data[3].output
        sparse_input_4 = block_data[4].output
        sparse_input_5 = block_data[5].output
        sparse_input_6 = block_data[6].output
        sparse_input_7 = block_data[7].output
        new_fea = block_data[8].output
        search_goods_fea = block_data[9].output
        item_add_feas = block_data[10].output
        cid_add_feas=block_data[11].output
        sparse_add_fea=block_data[12].output
        ext_ad_action_fea = block_data[13].output
        ext_acc_fea = block_data[14].output

        dense_input_raw = block_data[27].output
        dense_1 = tf.slice(dense_input_raw, [0, 0], [-1, 166])
        drop_emb = tf.slice(dense_input_raw, [0, 166], [-1, 256])
        add_user_fea_dense = tf.slice(dense_input_raw, [0, 166 + 256], [-1, 192])
        dense_2 = tf.slice(dense_input_raw, [0, 166 + 256 + 192], [-1, 987])
        ocpc_flag=tf.slice(dense_input_raw, [0, 166 + 256 + 192+987], [-1, 3])
        ocpc_flag = tf.matmul(ocpc_flag, trans_map)
        cid_mask = tf.slice(ocpc_flag, [0, 2], [-1, 1])
        cid_mask = tf.reshape(cid_mask, [-1])

        storewide_mask = tf.slice(dense_input_raw, [0, 166 + 256 + 192 + 987 + 3 + 216 + 1], [-1, 1])
        storewide_mask = tf.reshape(storewide_mask, [-1])

        cid_mask = cid_mask * (1.0 - storewide_mask)

        dense_input = tf.concat([dense_1, dense_2, add_user_fea_dense], -1) + tf.reduce_sum(drop_emb, axis=1, keepdims=True) * 0.0 + tf.reduce_sum(ocpc_flag, axis=1, keepdims=True) * 0.0
        logger.info("dense_input shape: {}".format(dense_input.get_shape()))

        dense_add_fea = tf.slice(dense_input_raw, [0, 166 + 256 + 192 + 987+3], [-1, 216])
        add_user_pay_fea=tf.slice(sparse_add_fea,[0,0],[-1,4*16])
        add_cpg_fea=tf.slice(sparse_add_fea,[0,4*16],[-1,13*16])
        add_new_fea =  tf.concat([add_user_pay_fea,dense_add_fea],-1)

        dense_account_select = tf.slice(dense_input_raw, [0, 166 + 256 + 192 + 987 + 3 + 216], [-1, 1])
        ext_industry_fea = tf.concat([ext_ad_action_fea, tf.multiply(ext_acc_fea, dense_account_select)], axis=1)

        sample_weight = 1.0 - cid_mask
        n_samples = tf.reduce_sum(sample_weight)
        search_fea = tf.slice(search_goods_fea, [0, 0], [-1, 8 * 16])
        goods_fea = tf.slice(search_goods_fea, [0, 8 * 16], [-1, 18 * 16])
        user_embedding_slot = self.get_slice(sparse_input_0,0,embedding_size)
        photo_embedding_slot = self.get_slice(sparse_input_7,101,embedding_size)
        item_spu_embedding_slot = self.get_slice(sparse_input_7,128,embedding_size)
        u_p_prob, u_i_prob, u_p_i_input = self.interpret_part(user_embedding_slot, photo_embedding_slot, item_spu_embedding_slot)

        sparse_zero = tf.slice(sparse_input_7, [0, 27 * 16], [-1, 16])
        #sparse_zero = tf.slice(sparse_input_0, [0, 0], [-1, 16]) * 0.0
        sparse_input = tf.concat([sparse_input_0, sparse_zero, sparse_input_1, sparse_zero, sparse_input_2, sparse_zero, sparse_zero,
                                sparse_input_3, sparse_zero, sparse_input_4, sparse_zero, sparse_input_5, sparse_zero, sparse_input_6,
                                sparse_zero, sparse_zero, sparse_zero, sparse_input_7], axis=1)
        # 4, 1, 6, 1, 42, 1, 1, 3, 1, 9, 1, 12, 1, 11, 1, 1, 1, 177
        logger.info("sparse_input shape: {}".format(sparse_input.get_shape()))

        slice_1 = tf.slice(sparse_input, [0, 0], [-1, 194 * 16])
        add_user_fea = tf.slice(sparse_input, [0, 194 * 16], [-1, 2 * 16])
        slice_2 = tf.slice(sparse_input, [0, 196 * 16], [-1, 30 * 16])

        gener_fea_1 = tf.slice(sparse_input, [0, 200 * 16], [-1, 48])
        gener_fea_2 = tf.slice(sparse_input, [0, 213 * 16], [-1, 16])
        gener_fea_3 = tf.slice(sparse_input, [0, 219 * 16], [-1, 16])
        gener_add = tf.slice(sparse_input, [0, 226 * 16], [-1, 160])
        slice_3 = tf.slice(sparse_input, [0, 226 * 16 + 160], [-1, 38 * 16])
        sparse_input = tf.concat([slice_1, slice_2, slice_3], -1)
        generalize_fea = tf.concat([gener_fea_1, gener_fea_2, gener_fea_3, gener_add], -1)

        long_term_pids = block_data[15].output
        long_term_aids = block_data[16].output
        long_term_play = block_data[17].output
        long_term_tags = block_data[18].output
        long_term_times = block_data[19].output

        logger.info("long_term_pids shape: {}".format(long_term_pids.get_shape()))
        logger.info("long_term_aids shape: {}".format(long_term_aids.get_shape()))

        akg_361 = block_data[20].output
        akg_362 = block_data[21].output
        akg_367 = block_data[22].output
        akg_368 = block_data[23].output
        akg_371 = block_data[24].output

        shark_emb_photo = block_data[25].output
        shark_emb_item = block_data[26].output
        shark_emb = tf.concat([shark_emb_photo, shark_emb_item], axis=1)

        cot_user = block_data[28].output
        cot_photo = block_data[29].output
        cot_sparse = tf.concat([cot_user, cot_photo], axis=1)
        cot_dense = block_data[30].output

        price_range_fea = block_data[31].output
        purchase_degree_dense = tf.slice(dense_input_raw, [0, 166 + 256 + 192 + 987 + 3 + 216 + 1], [-1, 219])
        purchase_degree_dense = tf.clip_by_value(purchase_degree_dense, tf.constant(0.0), tf.constant(1000.0))

        user_coupon = block_data[33].output
        selftrain_debias = block_data[34].output

        long_term_pids_new = tf.reshape(long_term_pids, [-1, 50, 2, 16])
        long_term_pids_new = tf.reduce_mean(long_term_pids_new, axis=2)

        long_term_aids_new = tf.reshape(long_term_aids, [-1, 50, 2, 16])
        long_term_aids_new = tf.reduce_mean(long_term_aids_new, axis=2)

        long_term_play_new = tf.reshape(long_term_play, [-1, 50, 2, 8])
        long_term_play_new = tf.reduce_mean(long_term_play_new, axis=2)

        long_term_tags_new = tf.reshape(long_term_tags, [-1, 50, 2, 8])
        long_term_tags_new = tf.reduce_mean(long_term_tags_new, axis=2)

        long_term_times_new = tf.reshape(long_term_times, [-1, 50, 2, 8])
        long_term_times_new = tf.reduce_mean(long_term_times_new, axis=2)

        long_term_pids_new = tf.reshape(long_term_pids_new, [-1, 800])
        long_term_aids_new = tf.reshape(long_term_aids_new, [-1, 800])
        long_term_play_new = tf.reshape(long_term_play_new, [-1, 400])
        long_term_tags_new = tf.reshape(long_term_tags_new, [-1, 400])
        long_term_times_new = tf.reshape(long_term_times_new, [-1, 400])

        long_seq_embedding_units = (800 + 800 + 400 + 400 + 400)
        long_seq_embedding_new = tf.concat(
            [long_term_pids_new, long_term_aids_new, long_term_play_new, long_term_tags_new, long_term_times_new], 1)
        logger.info("long_term_aids shape: {}".format(long_term_aids.get_shape()))

        akg_361_new = tf.reshape(akg_361, [-1, 320])
        akg_362_new = tf.reshape(akg_362, [-1, 320])
        akg_367_new = tf.reshape(akg_367, [-1, 160])
        akg_368_new = tf.reshape(akg_368, [-1, 800])
        akg_371_new = tf.reshape(akg_371, [-1, 160])
        akg_user_seq_embedding_units = 320 * 2 + 160 + 800
        akg_combine_seq_embedding_units = 160
        akg_seq_embedding = tf.concat([akg_361_new, akg_362_new, akg_367_new, akg_368_new, akg_371_new], 1)

        dnn_input = tf.concat([sparse_input, long_seq_embedding_new, dense_input, add_user_fea], axis=1)
        if ln:
            logger.info('ln in the embedding layer')
            dnn_input = tf.contrib.layers.layer_norm(dnn_input)
            generalize_input = tf.contrib.layers.layer_norm(generalize_fea, scope="generalize_layer")
            akg_seq_input = tf.contrib.layers.layer_norm(akg_seq_embedding, scope="akg_layer")
            new_fea = tf.contrib.layers.layer_norm(new_fea,scope="new_fea")
            search_input = tf.contrib.layers.layer_norm(search_fea, scope="search_feat_layer")
            goods_input = tf.contrib.layers.layer_norm(goods_fea, scope="goods_feat_layer")
            item_add_feas = tf.contrib.layers.layer_norm(item_add_feas, scope="item_add_feas_layer")
            shark_emb = tf.contrib.layers.layer_norm(shark_emb, scope="shark_emb_layer")
            cid_add_feas = tf.contrib.layers.layer_norm(cid_add_feas, scope="cid_add_feas_layer")
            add_new_fea = tf.contrib.layers.layer_norm(add_new_fea, scope="add_new_fea_layer")
            add_cpg_fea = tf.contrib.layers.layer_norm(add_cpg_fea, scope="add_cpg_fea_layer")
            ext_industry_fea = tf.contrib.layers.layer_norm(ext_industry_fea, scope="ext_industry_fea_layer")
            cot_sparse = tf.contrib.layers.layer_norm(cot_sparse, scope="cot_sparse_layer")
            cot_dense = tf.contrib.layers.layer_norm(cot_dense, scope="cot_dense_layer")
            price_range_fea = tf.contrib.layers.layer_norm(price_range_fea, scope="price_range_fea_layer")
            purchase_degree_dense = tf.contrib.layers.layer_norm(purchase_degree_dense, scope="purchase_degree_dense_layer")
            user_coupon = tf.contrib.layers.layer_norm(user_coupon, scope="user_coupon_layer")

        sparse_input = tf.slice(dnn_input, [0, 0], [-1, sparse_units])
        long_seq_embedding = tf.slice(dnn_input, [0, sparse_units], [-1, long_seq_embedding_units])
        dense_input = tf.slice(dnn_input, [0, sparse_units + long_seq_embedding_units], [-1, dense_units])
        add_feas = tf.slice(dnn_input, [0, sparse_units + long_seq_embedding_units + dense_units], [-1, add_user_fea_size * embedding_size])

        long_term_pids = tf.slice(long_seq_embedding, [0, 0], [-1, 800])
        long_term_aids = tf.slice(long_seq_embedding, [0, 800], [-1, 800])
        long_term_play = tf.slice(long_seq_embedding, [0, 1600], [-1, 400])
        long_term_tags = tf.slice(long_seq_embedding, [0, 2000], [-1, 400])
        long_term_times = tf.slice(long_seq_embedding, [0, 2400], [-1, 400])

        long_term_pids = tf.reshape(long_term_pids, [-1, 50, 16])
        long_term_aids = tf.reshape(long_term_aids, [-1, 50, 16])
        long_term_play = tf.reshape(long_term_play, [-1, 50, 8])
        long_term_tags = tf.reshape(long_term_tags, [-1, 50, 8])
        long_term_times = tf.reshape(long_term_times, [-1, 50, 8])

        long_term_pids_fake_seq1, long_term_pids_fake_seq2 = self.mask_and_dropout(long_term_pids, 50, 16)
        long_term_aids_fake_seq1, long_term_aids_fake_seq2 = self.mask_and_dropout(long_term_aids, 50, 16)
        long_term_play_fake_seq1, long_term_play_fake_seq2 = self.mask_and_dropout(long_term_play, 50, 8)
        long_term_tags_fake_seq1, long_term_tags_fake_seq2 = self.mask_and_dropout(long_term_tags, 50, 8)
        long_term_times_fake_seq1, long_term_times_fake_seq2 = self.mask_and_dropout(long_term_times, 50, 8)

        long_term_pids_fake_embed1 = self.ssl_tower(long_term_pids_fake_seq1, None, "long_term_pids_fake")
        long_term_pids_fake_embed2 = self.ssl_tower(long_term_pids_fake_seq2, None, "long_term_pids_fake")

        long_term_aids_fake_embed1 = self.ssl_tower(long_term_aids_fake_seq1, None, "long_term_aids_fake")
        long_term_aids_fake_embed2 = self.ssl_tower(long_term_aids_fake_seq2, None, "long_term_aids_fake")

        long_term_play_fake_embed1 = self.ssl_tower(long_term_play_fake_seq1, None, "long_term_play_fake")
        long_term_play_fake_embed2 = self.ssl_tower(long_term_play_fake_seq2, None, "long_term_play_fake")

        long_term_tags_fake_embed1 = self.ssl_tower(long_term_tags_fake_seq1, None, "long_term_tags_fake")
        long_term_tags_fake_embed2 = self.ssl_tower(long_term_tags_fake_seq2, None, "long_term_tags_fake")

        long_term_times_fake_embed1 = self.ssl_tower(long_term_times_fake_seq1, None, "long_term_times_fake")
        long_term_times_fake_embed2 = self.ssl_tower(long_term_times_fake_seq2, None, "long_term_times_fake")

        cl_loss = self.get_cl_loss(long_term_pids_fake_embed1, long_term_pids_fake_embed2) +\
                    self.get_cl_loss(long_term_aids_fake_embed1, long_term_aids_fake_embed2) +\
                    self.get_cl_loss(long_term_play_fake_embed1, long_term_play_fake_embed2) +\
                    self.get_cl_loss(long_term_tags_fake_embed1, long_term_tags_fake_embed2) +\
                    self.get_cl_loss(long_term_times_fake_embed1, long_term_times_fake_embed2)

        long_seq_embedding = tf.concat(
            [long_term_pids, long_term_aids, long_term_play, long_term_tags, long_term_times], 2)
        #akg
        akg_user_input = tf.slice(akg_seq_input, [0, 0], [-1, akg_user_seq_embedding_units])  # akg
        akg_combine_input = tf.slice(akg_seq_input, [0, akg_user_seq_embedding_units], [-1, akg_combine_seq_embedding_units])  # akg

        akg_361 = tf.slice(akg_user_input, [0, 0], [-1, 320])
        akg_362 = tf.slice(akg_user_input, [0, 320], [-1, 320])
        akg_367 = tf.slice(akg_user_input, [0, 640], [-1, 160])
        akg_368 = tf.slice(akg_user_input, [0, 800], [-1, 800])

        akg_371 = tf.slice(akg_combine_input, [0, 0], [-1, 160])

        akg_361 = tf.reshape(akg_361, [-1, 40, 8])
        akg_362 = tf.reshape(akg_362, [-1, 40, 8])
        akg_367 = tf.reshape(akg_367, [-1, 20, 8])
        akg_368 = tf.reshape(akg_368, [-1, 100, 8])
        akg_371 = tf.reshape(akg_371, [-1, 20, 8])

        akg_ui_seq = tf.concat([akg_361, akg_368], axis=1)

        sparse_input_reshape = tf.reshape(sparse_input, [-1, sparse_size, embedding_size])

        senet_embedding = self.senet_layer(sparse_input_reshape, sparse_size, se_pool,
                                           is_train, se_act, dropout_rate, use_bn, ln)

        senet_input = tf.reshape(senet_embedding, [-1, sparse_units])

        #akg
        with tf.variable_scope('akg_ui_attention_layer', reuse=tf.AUTO_REUSE):
            layer_size = 256
            _, target_embedding = self.fc1(tf.stop_gradient(senet_input), sparse_units, layer_size, True,
                                           is_train, dropout_rate, False, False)
            target_embedding = tf.reshape(target_embedding, (-1, 1, layer_size))
            akg_ui_att_output = self.transformer_component_v2(target_embedding, akg_ui_seq, layer_size, "akg_ui_seq_", nh=1)

        akg_pooling_emb_list = []
        for akg in [akg_361, akg_362, akg_367, akg_368, akg_371]:
            akg_pooling_emb_list.append(tf.reduce_mean(akg, axis=1))
        akg_pooling_emb = tf.concat(akg_pooling_emb_list, axis=-1)

        with tf.variable_scope('attention_layer', reuse=tf.AUTO_REUSE):
            layer_size = 1024
            _, target_embedding = self.fc1(tf.stop_gradient(senet_input), sparse_units, layer_size, True, is_train,
                                           dropout_rate, False, False)
            # _, target_embedding = self.fc1(senet_input, sparse_units, layer_size, True, is_train,
            #                                dropout_rate, False, False)
            target_embedding = tf.reshape(target_embedding, (-1, 1, layer_size))
            long_att_output = self.transformer_component_v2(target_embedding, long_seq_embedding, layer_size, "long_term_",
                                                         nh=1)

        cross_input, embedding_combine = self.cross_layer_new(senet_input, user_feature_size,
                                                          photo_feature_size,
                                                          combine_feature_size,
                                                          embedding_size)

        dnn_input = tf.concat([cross_input, embedding_combine, long_att_output, dense_input, akg_ui_att_output, akg_pooling_emb, add_feas, new_fea, search_input, goods_input, item_add_feas, shark_emb, u_p_i_input,cid_add_feas,add_new_fea,add_cpg_fea, ext_industry_fea, cot_sparse, cot_dense, price_range_fea, purchase_degree_dense, user_coupon], 1)

        input_size = dnn_input.get_shape().as_list()[1]
        generalize_input_size = generalize_input.get_shape().as_list()[1]

        input = dnn_input
        for i in range(self.share_num):
            with tf.variable_scope("share_bottom_layer_new_{}".format(i), reuse=tf.AUTO_REUSE):
                layer_size = self._dnn_net_size[i]
                input = self.fc(self._dnn_net_size, input, input_size, layer_size, i, is_train, use_bn, ln)
                input_size = layer_size
            with tf.variable_scope("generalize_layer_{}".format(i), reuse=tf.AUTO_REUSE):
                layer_size = self.general_tower[i]
                generalize_input = self.fc(self.general_tower, generalize_input, generalize_input_size, layer_size, i,
                                           is_train, use_bn, ln)
                generalize_input_size = layer_size
        # debias tower
        selftrain_debias_size = 16
        for i in range(len(self.debias_net_size)):
            with tf.variable_scope("debias_layer_{}".format(i), reuse=tf.AUTO_REUSE):
                layer_size = self.debias_net_size[i]
                selftrain_debias, w3 = self.fc_128(self.debias_net_size, selftrain_debias, selftrain_debias_size, layer_size, i, is_train, use_bn, ln)
                selftrain_debias_gate = 2 * tf.nn.sigmoid(selftrain_debias)
                selftrain_debias_size = layer_size
    
        cid_input = pxr_input = ped_input = other_input = input
        cid_input_size = pxr_input_size = ped_input_size = other_input_size = input_size

        input = tf.concat([input, tf.stop_gradient(generalize_input)], -1)
        input_size = input_size + generalize_input_size

        # poso gate net
        input = tf.multiply(input, selftrain_debias_gate)
  
        
        # qcpx_shared_bottom_input
        qcpx_input = input
        qcpx_input_size = input_size

        for i in range(self.share_num, len(self.general_tower)):
            with tf.variable_scope("generalize_layer_{}".format(i), reuse=tf.AUTO_REUSE):
                layer_size = self.general_tower[i]
                generalize_input = self.fc(self.general_tower, generalize_input, generalize_input_size, layer_size, i,
                                           is_train,
                                           use_bn, ln)
                generalize_input_size = layer_size
        gen_loss = tf.divide(
            tf.reduce_sum(sample_weight * tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_ctcvr,
                                                                                         logits=generalize_input)), n_samples,
            name='gen_xentropy')

        # item interest
        item_interest_cross_entropy = tf.divide(
            tf.reduce_sum(sample_weight * label_item_mask * tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_item,
                                                                                         logits=u_i_prob)), tf.reduce_sum(sample_weight * label_item_mask) + 1e-8,
            name='item_interest_xentropy')
        # photo interest
        photo_interest_cross_entropy = tf.divide(
            tf.reduce_sum(sample_weight * label_photo_mask * tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_photo,
                                                                                         logits=u_p_prob)), tf.reduce_sum(sample_weight * label_photo_mask) + 1e-8,
            name='item_interest_xentropy')
        interest_loss = 0.1*(item_interest_cross_entropy + photo_interest_cross_entropy)

        # pxr_head
        for i in range(self.share_num, len(self._dnn_net_size)):
            with tf.variable_scope("pxr_layer_{}".format(i), reuse=tf.AUTO_REUSE):
                layer_size = self._dnn_net_size[i]
                pxr_input = self.fc(self._dnn_net_size, pxr_input, pxr_input_size, layer_size, i, is_train, use_bn, ln)
                pxr_input_size = layer_size
        # pxr_prob = tf.nn.softmax(pxr_input, name="pxr_softmax")
        pxr_cross_entropy = tf.divide(
            tf.reduce_sum(sample_weight * tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_pxr,
                                                                                         logits=pxr_input)), n_samples,
            name='pxr_xentropy')

        # ped_head
        for i in range(self.share_num, len(self._dnn_net_size)):
            with tf.variable_scope("ped_layer_{}".format(i), reuse=tf.AUTO_REUSE):
                layer_size = self._dnn_net_size[i]
                ped_input = self.fc(self._dnn_net_size, ped_input, ped_input_size, layer_size, i, is_train, use_bn, ln)
                ped_input_size = layer_size
        # ped_prob = tf.nn.softmax(ped_input, name="ped_softmax")
        ped_cross_entropy = tf.divide(
            tf.reduce_sum(sample_weight * tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_playend,
                                                                                         logits=ped_input)), n_samples,
            name='ped_xentropy')

        # ctr head
        for i in range(self.share_num, len(self._dnn_net_size)):
            with tf.variable_scope("ctr_upper_layer_{}".format(i), reuse=tf.AUTO_REUSE):
                layer_size = self._dnn_net_size[i]
                other_input = self.fc(self._dnn_net_size, other_input, other_input_size, layer_size, i, is_train,
                                      use_bn, ln)
                other_input_size = layer_size

        # other_prob = tf.nn.softmax(other_input, name="ctr_softmax")
        other_cross_entropy = tf.divide(
            tf.reduce_sum(sample_weight * tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_ctr,
                                                                                         logits=other_input)),
            n_samples, name='ctr_xentropy')

        cid_weight = tf.cast(cid_mask, tf.float32)
        cid_samples = tf.reduce_sum(cid_weight)
        for i in range(self.share_num, len(self._dnn_net_size)):
            with tf.variable_scope("cid_layer_{}".format(i), reuse=tf.AUTO_REUSE):
                layer_size = self._dnn_net_size[i]
                cid_input = self.fc(self._dnn_net_size, cid_input, cid_input_size, layer_size, i, is_train, use_bn, ln)
                cid_input_size = layer_size
        cid_prob = tf.nn.softmax(cid_input, name="cid_softmax")
        cid_cross_entropy = tf.divide(
            tf.reduce_sum(cid_mask * tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label_cid,
                                                                                         logits=cid_input)), cid_samples,
            name='p2s_xentropy')

        # ctcvr head
        for i in range(self.share_num, len(self._dnn_net_size)):
            with tf.variable_scope("upper_layer_{}".format(i), reuse=tf.AUTO_REUSE):
                layer_size = self._dnn_net_size[i]
                if i==1:
                    input, temp_o1, w1, normalized1, gamma1, std1, ori_input1 = self.fc_256(self._dnn_net_size, input, input_size, layer_size, i, is_train, use_bn, ln)
                elif i==2:
                    input, temp_o2, w2, normalized, gamma, std, ori_input = self.fc_256(self._dnn_net_size, input, input_size, layer_size, i, is_train, use_bn, ln)
                elif i==3:
                    input, w3 = self.fc_128(self._dnn_net_size, input, input_size, layer_size, i, is_train, use_bn, ln)
                if i == 1:
                    temp_input = input
                    temp_size = layer_size
                input_size = layer_size
        
        # qcpx shared bottom: qcpx_input
        qcpx_share_bottom = qcpx_input
        qcpx_share_bottom_size = qcpx_input_size
        # qcpx c0 head
        qcpx_c0_input = qcpx_c1_prime_input = qcpx_c1_input = qcpx_c2_input = qcpx_c3_input = qcpx_c4_input = qcpx_c5_input = qcpx_share_bottom
        qcpx_c0_input_size = qcpx_c1_prime_input_size = qcpx_c1_input_size = qcpx_c2_input_size = qcpx_c3_input_size = qcpx_c4_input_size = qcpx_c5_input_size = qcpx_share_bottom_size

        for i in range(len(self.qcpx_net_size)):
            with tf.variable_scope("qcpx_tower_c0_{}".format(i), reuse=tf.AUTO_REUSE):
                layer_size = self.qcpx_net_size[i]
                if i==0:
                    qcpx_c0_input, temp_o1, w1, normalized1, gamma1, std1, ori_input1 = self.fc_256(self.qcpx_net_size, qcpx_c0_input, qcpx_c0_input_size, layer_size, i, is_train, use_bn, ln)
                elif i==1:
                    qcpx_c0_input, temp_o2, w2, normalized, gamma, std, ori_input = self.fc_256(self.qcpx_net_size, qcpx_c0_input, qcpx_c0_input_size, layer_size, i, is_train, use_bn, ln)
                elif i==2:
                    qcpx_c0_nn, w3 = self.fc_128(self.qcpx_net_size, qcpx_c0_input, qcpx_c0_input_size, layer_size, i, is_train, use_bn, ln)
                qcpx_c0_input_size = layer_size
        
        # qcpx c0 hea
        ## f'(x)
        for i in range(len(self.qcpx_net_size)):
            with tf.variable_scope("qcpx_tower_c0_{}".format(i), reuse=tf.AUTO_REUSE):
                layer_size = self.qcpx_net_size[i]
                if i==0:
                    qcpx_c1_prime_input, temp_o1, w1, normalized1, gamma1, std1, ori_input1 = self.fc_256(self.qcpx_net_size, qcpx_c1_prime_input, qcpx_c1_prime_input_size, layer_size, i, is_train, use_bn, ln)
                elif i==1:
                    qcpx_c1_prime_input, temp_o2, w2, normalized, gamma, std, ori_input = self.fc_256(self.qcpx_net_size, qcpx_c1_prime_input, qcpx_c1_prime_input_size, layer_size, i, is_train, use_bn, ln)
                elif i==2:
                    qcpx_c1_prime_nn, w3 = self.fc_128(self.qcpx_net_size, qcpx_c1_prime_input, qcpx_c1_prime_input_size, layer_size, i, is_train, use_bn, ln)
                qcpx_c1_prime_input_size = layer_size 
        # qcpx c1 head
        for i in range(len(self.qcpx_net_size)):
            with tf.variable_scope("qcpx_tower_c1_{}".format(i), reuse=tf.AUTO_REUSE):
                layer_size = self.qcpx_net_size[i]
                if i==0:
                    qcpx_c1_input, temp_o1, w1, normalized1, gamma1, std1, ori_input1 = self.fc_256(self.qcpx_net_size, qcpx_c1_input, qcpx_c1_input_size, layer_size, i, is_train, use_bn, ln)
                elif i==1:
                    qcpx_c1_input, temp_o2, w2, normalized, gamma, std, ori_input = self.fc_256(self.qcpx_net_size, qcpx_c1_input, qcpx_c1_input_size, layer_size, i, is_train, use_bn, ln)
                elif i==2:
                    qcpx_c1_nn, w3 = self.fc_128(self.qcpx_net_size, qcpx_c1_input, qcpx_c1_input_size, layer_size, i, is_train, use_bn, ln)
                qcpx_c1_input_size = layer_size

        # qcpx c2 hea
        for i in range(len(self.qcpx_net_size)):
            with tf.variable_scope("qcpx_tower_c2_{}".format(i), reuse=tf.AUTO_REUSE):
                layer_size = self.qcpx_net_size[i]
                if i==0:
                    qcpx_c2_input, temp_o1, w1, normalized1, gamma1, std1, ori_input1 = self.fc_256(self.qcpx_net_size, qcpx_c2_input, qcpx_c2_input_size, layer_size, i, is_train, use_bn, ln)
                elif i==1:
                    qcpx_c2_input, temp_o2, w2, normalized, gamma, std, ori_input = self.fc_256(self.qcpx_net_size, qcpx_c2_input, qcpx_c2_input_size, layer_size, i, is_train, use_bn, ln)
                elif i==2:
                    qcpx_c2_nn, w3 = self.fc_128(self.qcpx_net_size, qcpx_c2_input, qcpx_c2_input_size, layer_size, i, is_train, use_bn, ln)
                qcpx_c2_input_size = layer_size

        # qcpx c3 hea
        for i in range(len(self.qcpx_net_size)):
            with tf.variable_scope("qcpx_tower_c3_{}".format(i), reuse=tf.AUTO_REUSE):
                layer_size = self.qcpx_net_size[i]
                if i==0:
                    qcpx_c3_input, temp_o1, w1, normalized1, gamma1, std1, ori_input1 = self.fc_256(self.qcpx_net_size, qcpx_c3_input, qcpx_c3_input_size, layer_size, i, is_train, use_bn, ln)
                elif i==1:
                    qcpx_c3_input, temp_o2, w2, normalized, gamma, std, ori_input = self.fc_256(self.qcpx_net_size, qcpx_c3_input, qcpx_c3_input_size, layer_size, i, is_train, use_bn, ln)
                elif i==2:
                    qcpx_c3_nn, w3 = self.fc_128(self.qcpx_net_size, qcpx_c3_input, qcpx_c3_input_size, layer_size, i, is_train, use_bn, ln)
                qcpx_c3_input_size = layer_size
        
        # qcpx c4 hea
        for i in range(len(self.qcpx_net_size)):
            with tf.variable_scope("qcpx_tower_c4_{}".format(i), reuse=tf.AUTO_REUSE):
                layer_size = self.qcpx_net_size[i]
                if i==0:
                    qcpx_c4_input, temp_o1, w1, normalized1, gamma1, std1, ori_input1 = self.fc_256(self.qcpx_net_size, qcpx_c4_input, qcpx_c4_input_size, layer_size, i, is_train, use_bn, ln)
                elif i==1:
                    qcpx_c4_input, temp_o2, w2, normalized, gamma, std, ori_input = self.fc_256(self.qcpx_net_size, qcpx_c4_input, qcpx_c4_input_size, layer_size, i, is_train, use_bn, ln)
                elif i==2:
                    qcpx_c4_nn, w3 = self.fc_128(self.qcpx_net_size, qcpx_c4_input, qcpx_c4_input_size, layer_size, i, is_train, use_bn, ln)
                qcpx_c4_input_size = layer_size
        
        # qcpx c5 hea
        for i in range(len(self.qcpx_net_size)):
            with tf.variable_scope("qcpx_tower_c5_{}".format(i), reuse=tf.AUTO_REUSE):
                layer_size = self.qcpx_net_size[i]
                if i==0:
                    qcpx_c5_input, temp_o1, w1, normalized1, gamma1, std1, ori_input1 = self.fc_256(self.qcpx_net_size, qcpx_c5_input, qcpx_c5_input_size, layer_size, i, is_train, use_bn, ln)
                elif i==1:
                    qcpx_c5_input, temp_o2, w2, normalized, gamma, std, ori_input = self.fc_256(self.qcpx_net_size, qcpx_c5_input, qcpx_c5_input_size, layer_size, i, is_train, use_bn, ln)
                elif i==2:
                    qcpx_c5_nn, w3 = self.fc_128(self.qcpx_net_size, qcpx_c5_input, qcpx_c5_input_size, layer_size, i, is_train, use_bn, ln)
                qcpx_c5_input_size = layer_size

        # user coupon feature head
        coupon_cross_input = tf.concat([user_coupon, goods_input, item_add_feas, sparse_input_6], -1)
        user_coupon_c0_input =  coupon_cross_input
        user_coupon_c0_input_size =  coupon_cross_input.get_shape().as_list()[1]

        for i in range(len(self.user_coupon_net_size)):
            with tf.variable_scope("user_coupon_tower_c0_{}".format(i), reuse=tf.AUTO_REUSE):
                layer_size = self.user_coupon_net_size[i]
                user_coupon_c0_input, w3 = self.fc_128(self.user_coupon_net_size, user_coupon_c0_input, user_coupon_c0_input_size, layer_size, i, is_train, use_bn, ln)
                user_coupon_c0_input_size = layer_size
        user_coupon_c0_nn = user_coupon_c0_input
        c0 = tf.math.tanh(tf.add_n([qcpx_c0_nn, user_coupon_c0_nn])/5.0)
        c0 = tf.identity(c0, name='elastic_c0')
        c1 = qcpx_c1_nn / 5.0
        c1 = tf.identity(c1, name='elastic_c1')
        c2 = qcpx_c2_nn / 5.0
        c2 = tf.identity(c2, name='elastic_c2')
        c3 = qcpx_c3_nn / 5.0
        c3 = tf.identity(c3, name='elastic_c3')
        c4 = qcpx_c4_nn / 5.0
        c4 = tf.identity(c4, name='elastic_c4')
        c5 = qcpx_c5_nn / 5.0
        c5 = tf.identity(c5, name='elastic_c5')
        c1_prime = qcpx_c1_prime_nn / 5.0
        c1_prime = tf.identity(c1_prime, name='elastic_c1_prime')

        float_label_ctcvr = tf.cast(label_ctcvr, tf.float32)
        y_pred_qcpx = tf.reshape(float_label_ctcvr, [-1])

        cross_entropy_qcpx = tf.divide(
            tf.reduce_sum(sample_weight * tf.nn.sigmoid_cross_entropy_with_logits(labels=float_label_ctcvr, logits=tf.zeros_like(y_pred_qcpx))), 
            n_samples, 
            name='qcpx_entropy'
        )
        
        if self.trainable_task_weight:
            w_ctcvr = tf.get_variable('w_ctcvr', [1],
                                      initializer=tf.zeros_initializer,
                                      trainable=True)
            w_ctr = tf.get_variable('w_ctr', [1],
                                    initializer=tf.zeros_initializer,
                                    trainable=True)
            train_loss = cross_entropy_qcpx*tf.exp(-w_ctcvr) + 0.5*w_ctcvr

        self.prob = tf.clip_by_value(y_pred_qcpx, tf.constant(0.0), tf.constant(1.0))
        self.label = tf.cast(label_ctcvr, tf.float32)
        with tf.device('/cpu:0'):
            _, self.auc, reset_auc_eval = auc_eval(label_ctcvr,
                                                   tf.clip_by_value(y_pred_qcpx, tf.constant(0.0), tf.constant(1.0)),
                                                   weights=sample_weight, name="auc")
        self.eval_targets = [("ctcvr", tf.clip_by_value(y_pred_qcpx, tf.constant(0.0), tf.constant(1.0)), self.label, sample_weight, "auc"),]
        targets, prediction = metric_merge(self.eval_targets)
        return {
                   "loss": train_loss,
                   "targets": targets,
                   "auc": self.auc,
               }, self.eval_targets, prediction, self.GetPlaceHolder()